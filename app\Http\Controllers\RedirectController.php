<?php

namespace App\Http\Controllers;

use App\Models\ShortUrl;
use Illuminate\Support\Facades\Auth;

class RedirectController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Handle short URL redirect.
     */
    public function redirect($shortCode)
    {
        $user = Auth::user();
        $shortUrl = ShortUrl::where('short_code', $shortCode)->with(['user'])->first();

        if (!$shortUrl) {
            abort(404, 'Short URL not found');
        }

        if (!$shortUrl->isAccessible()) {
            abort(410, 'Short URL is no longer available');
        }

        // Check if user can access this URL based on role and company
        $this->authorizeAccess($user, $shortUrl);

        // Increment click count
        $shortUrl->incrementClicks();

        // Redirect to original URL
        return redirect($shortUrl->original_url);
    }

    /**
     * Check if user can access the short URL.
     */
    private function authorizeAccess($user, $shortUrl)
    {
        // SuperAdmin can access all URLs
        if ($user->hasRole('superAdmin')) {
            return;
        }

        // Admin can access URLs from their company
        if ($user->hasRole('admin')) {
            if (!$user->company_id || $shortUrl->user->company_id !== $user->company_id) {
                abort(403, 'You can only access URLs from your company.');
            }
            return;
        }

        // Members can only access their own URLs
        if ($user->hasRole('user')) {
            if ($shortUrl->user_id !== $user->id) {
                abort(403, 'You can only access your own URLs.');
            }
            return;
        }

        abort(403, 'Access denied.');
    }
}
