<?php

namespace App\Http\Controllers;

use App\Models\ShortUrl;

class RedirectController extends Controller
{
    /**
     * Handle short URL redirect.
     */
    public function redirect($shortCode)
    {
        $shortUrl = ShortUrl::where('short_code', $shortCode)->first();

        if (!$shortUrl) {
            abort(404, 'Short URL not found');
        }

        if (!$shortUrl->isAccessible()) {
            abort(410, 'Short URL is no longer available');
        }

        // Increment click count
        $shortUrl->incrementClicks();

        // Redirect to original URL
        return redirect($shortUrl->original_url);
    }
}
