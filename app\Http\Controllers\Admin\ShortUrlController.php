<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ShortUrl;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ShortUrlController extends Controller
{
    public function __construct()
    {
        // $this->middleware('permission:read_short_urls')->only(['index', 'show']);
        // $this->middleware('permission:create_short_urls')->only(['create', 'store']);
        // $this->middleware('permission:update_short_urls')->only(['edit', 'update']);
        // $this->middleware('permission:delete_short_urls')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ShortUrl::with(['user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('original_url', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhere('short_code', 'like', "%{$search}%");
            });
        }

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->status === 'expired') {
                $query->where('expires_at', '<', now());
            }
        }

        $shortUrls = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/ShortUrls/Index', [
            'shortUrls' => $shortUrls,
            'filters' => $request->only(['search', 'user_id', 'status']),
            'users' => User::select('id', 'name', 'email')->get(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/ShortUrls/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'original_url' => 'required|url|max:2048',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            // 'user_id' => 'required|exists:users,id',
            'short_code' => 'nullable|string|max:20|unique:short_urls,short_code',
            'is_active' => 'boolean',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $shortCode = $request->short_code ?: ShortUrl::generateShortCode();

        $shortUrl = ShortUrl::create([
            'user_id' => Auth::id(),
            'original_url' => $request->original_url,
            'title' => $request->title,
            'description' => $request->description,
            'short_code' => $shortCode,
            'is_active' => $request->boolean('is_active', true),
            'expires_at' => $request->expires_at,
        ]);

        return redirect()->route('admin.short-urls.index')
            ->with('success', 'Short URL created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ShortUrl $shortUrl)
    {
        $shortUrl->load(['user']);

        return Inertia::render('Admin/ShortUrls/Show', [
            'shortUrl' => $shortUrl,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ShortUrl $shortUrl)
    {
        $shortUrl->load(['user']);

        return Inertia::render('Admin/ShortUrls/Edit', [
            'shortUrl' => $shortUrl,
            'users' => User::select('id', 'name', 'email')->get(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ShortUrl $shortUrl)
    {
        $request->validate([
            'original_url' => 'required|url|max:2048',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'user_id' => 'required|exists:users,id',
            'short_code' => 'required|string|max:20|unique:short_urls,short_code,' . $shortUrl->id,
            'is_active' => 'boolean',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $shortUrl->update([
            'user_id' => $request->user_id,
            'original_url' => $request->original_url,
            'title' => $request->title,
            'description' => $request->description,
            'short_code' => $request->short_code,
            'is_active' => $request->boolean('is_active', true),
            'expires_at' => $request->expires_at,
        ]);

        return redirect()->route('admin.short-urls.index')
            ->with('success', 'Short URL updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ShortUrl $shortUrl)
    {
        $shortUrl->delete();

        return redirect()->route('admin.short-urls.index')
            ->with('success', 'Short URL deleted successfully.');
    }
}
