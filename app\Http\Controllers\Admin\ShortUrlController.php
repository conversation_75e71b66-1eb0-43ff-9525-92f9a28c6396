<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ShortUrl;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ShortUrlController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $user = Auth::user();

            // SuperAdmin cannot access short URL management
            if ($user->hasRole('superAdmin')) {
                abort(403, 'SuperAdmin users cannot manage short URLs.');
            }

            // Only admin and user roles can access
            if (!$user->hasRole(['admin', 'user'])) {
                abort(403, 'Access denied.');
            }

            return $next($request);
        });
    }

    /**
     * Check if user can access the short URL based on role and company.
     */
    private function authorizeShortUrl(ShortUrl $shortUrl)
    {
        $user = Auth::user();

        if ($user->hasRole('admin')) {
            // Admin can only access URLs from users in their company
            if (!$user->company_id || $shortUrl->user->company_id !== $user->company_id) {
                abort(403, 'You can only access URLs from your company.');
            }
        } elseif ($user->hasRole('user')) {
            // Members can only access their own URLs
            if ($shortUrl->user_id !== $user->id) {
                abort(403, 'You can only access your own URLs.');
            }
        }
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = ShortUrl::with(['user']);

        // Apply role-based filtering
        if ($user->hasRole('admin')) {
            // Admin can only see URLs from users in their company
            if ($user->company_id) {
                $query->whereHas('user', function ($q) use ($user) {
                    $q->where('company_id', $user->company_id);
                });
            } else {
                // If admin has no company, they see no URLs
                $query->where('id', 0); // This will return no results
            }
        } elseif ($user->hasRole('user')) {
            // Members can only see their own URLs
            $query->where('user_id', $user->id);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('original_url', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhere('short_code', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->status === 'expired') {
                $query->where('expires_at', '<', now());
            }
        }

        $shortUrls = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/ShortUrls/Index', [
            'shortUrls' => $shortUrls,
            'filters' => $request->only(['search', 'status']),
            'userRole' => $user->roles->first()->name ?? 'user',
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/ShortUrls/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'original_url' => 'required|url|max:2048',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            // 'user_id' => 'required|exists:users,id',
            'short_code' => 'nullable|string|max:20|unique:short_urls,short_code',
            'is_active' => 'boolean',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $shortCode = $request->short_code ?: ShortUrl::generateShortCode();

        $shortUrl = ShortUrl::create([
            'user_id' => Auth::id(),
            'original_url' => $request->original_url,
            'title' => $request->title,
            'description' => $request->description,
            'short_code' => $shortCode,
            'is_active' => $request->boolean('is_active', true),
            'expires_at' => $request->expires_at,
        ]);

        return redirect()->route('admin.short-urls.index')
            ->with('success', 'Short URL created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ShortUrl $shortUrl)
    {
        $this->authorizeShortUrl($shortUrl);

        $shortUrl->load(['user']);

        return Inertia::render('Admin/ShortUrls/Show', [
            'shortUrl' => $shortUrl,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ShortUrl $shortUrl)
    {
        $this->authorizeShortUrl($shortUrl);

        $shortUrl->load(['user']);

        return Inertia::render('Admin/ShortUrls/Edit', [
            'shortUrl' => $shortUrl,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ShortUrl $shortUrl)
    {
        $this->authorizeShortUrl($shortUrl);

        $request->validate([
            'original_url' => 'required|url|max:2048',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'short_code' => 'required|string|max:20|unique:short_urls,short_code,' . $shortUrl->id,
            'is_active' => 'boolean',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $shortUrl->update([
            'original_url' => $request->original_url,
            'title' => $request->title,
            'description' => $request->description,
            'short_code' => $request->short_code,
            'is_active' => $request->boolean('is_active', true),
            'expires_at' => $request->expires_at,
        ]);

        return redirect()->route('admin.short-urls.index')
            ->with('success', 'Short URL updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ShortUrl $shortUrl)
    {
        $this->authorizeShortUrl($shortUrl);

        $shortUrl->delete();

        return redirect()->route('admin.short-urls.index')
            ->with('success', 'Short URL deleted successfully.');
    }
}
